<?php
namespace TXI;

/**
 * @package TXI
 */
class Globals
{
    const NAME = 'TXI Systems';
    const HTML_TAB = '     ';
    const HTML_RETURN = '&#13;&#10;';
    const TEXT_TAB = '     ';
    const TEXT_RETURN = '\n';

    const MYSQL_DATE_FORMAT = 'Y-m-d H:i:s';
    const MYSQL_DATE_ONLY_FORMAT = 'Y-m-d';
    const DATE_TIME_FORMAT_DISPLAY = 'n/j/y H:i:s';
    const DATE_FORMAT_DISPLAY = 'n/j/y';

    //Modules data formatting
    const MODULES_HEADER_START = 1;
    const MODULES_HEADER_TYPE_SEPARATOR = 7;
    const MODULES_HEADER_SEPARATOR = 2;
    const MODULES_HEADER_END = 3;
    const MODULES_ERROR_START = 8;
    const MODULES_ERROR_TYPE_STANDARD = 0;
    const MODULES_ERROR_END = 6;
    const MODULES_DATA_START = 4;
    const MODULES_DATA_SEPARATOR = 2;
    const MODULES_DATA_EOL = 5;
    const MODULES_DATA_END = 6;
    const MODULES_DATA_NULL = 7;

    const TEMP_DATA_PATH = "/mnt/FileStorage/TXIData/temp/";

    // Writes information the sytem log
    static function write_to_log($sApplication, $sMessage, $sContext = "", $iMessageType = Log::INFO_LOG)
    {
        //Assumes that syslog (logger) is set up on server
        try {
            switch ($iMessageType) {
                case Log::INFO_LOG:
                    $iPriority = \LOG_INFO;
                    break;
                case Log::INFO_DEBUG:
                    $iPriority = \LOG_DEBUG;
                    break;
                case Log::INFO_GENERAL:
                    $iPriority = \LOG_INFO;
                    break;
                case Log::INFO_LOG:
                    $iPriority = \LOG_INFO;
                    break;
                case Log::INFO_DATA_IO:
                    $iPriority = \LOG_INFO;
                    break;
                case Log::INFO_WARNING:
                    $iPriority = \LOG_WARNING;
                    break;
                case Log::ERROR_GENERAL:
                    $iPriority = \LOG_ERR;
                    break;
                case Log::ERROR_EXCEPTION:
                    $iPriority = \LOG_ALERT;
                    break;
                case Log::ERROR_EXCEPTION_CRITICAL:
                    $iPriority = \LOG_CRIT;
                    break;
                case Log::ERROR_CONNECTION:
                    $iPriority = \LOG_ALERT;
                    break;
                case Log::ERROR_DATABASE_GENERAL:
                    $iPriority = \LOG_ALERT;
                    break;
                case Log::ERROR_DATABASE_CONNECTION:
                    $iPriority = \LOG_ALERT;
                    break;
                case Log::ERROR_DATABASE_QUERY:
                    $iPriority = \LOG_ERR;
                    break;
                case Log::ERROR_DATABASE_CRITICAL:
                    $iPriority = \LOG_CRIT;
                    break;
                case Log::ERROR_LOGIC_GENERAL:
                    $iPriority = \LOG_ERR;
                    break;
                case Log::ERROR_LOGIC_CRITICAL:
                    $iPriority = \LOG_CRIT;
                    break;
                default:
                    $iPriority = \LOG_INFO;
            }
            $sLog = "[".$sApplication."](".$sContext.")".$sMessage;
            if (strlen($sLog) > 500) {
                for ($iPos = 0; $iPos < strlen($sLog); $iPos += 500) {
                    syslog($iPriority, substr($sLog, $iPos, 500));
                }
            } else
                syslog($iPriority, $sLog);
        } catch (\Exception $exc) {
        }
    }

    /**
     * @param string $sKey
     * @param array $a
     * @param string $sDefault
     * @return string
     */
    static function get_array_value($sKey, $a, $sDefault = "")
    {
        if (\array_key_exists($sKey, $a)) {
            if (\is_null($a[(string)$sKey]))
                return $sDefault;
            else
                return $a[(string)$sKey];
        } else
            return $sDefault;
    }

    /**
     * @param string $sKey
     * @param array $a
     * @param bool $bDefault
     * @return string
     */
    static function get_boolean_array_value($sKey, $a, $bDefault = false)
    {
        if (\array_key_exists($sKey, $a)) {
            if ($a[(string)$sKey] === true || strtoupper($a[(string)$sKey]) == "TRUE" || $a[(string)$sKey] == "1")
                return true;
            else
                return false;
        } else
            return $bDefault;
    }

    /**
     * @param \SimpleXMLElement $elemParent
     * @param string $sRedundancy if there are redundant elements in the XML then specify that here
     * @return array
     */
    static function XML_to_array($elemParent, $sRedundancy = "")
    {
        if ($elemParent instanceof \SimpleXMLElement) {
            $elemChildren = $elemParent->children();
            $a = null;
        }

        foreach ($elemChildren as $element => $value) {
            if ($value instanceof \SimpleXMLElement) {
                $values = (array)$value->children();
                if (count($values) > 0) {
                    if ($element == $sRedundancy && $sRedundancy != "") {
                        $a[] = \TXI\Globals::XML_to_array($value, $sRedundancy);
                    } else {
                        $a[$element] = \TXI\Globals::XML_to_array($value, $sRedundancy);
                    }
                } else {
                    if (!isset($a[$element])) {
                        $a[$element] = (string)$value;
                    } else {
                        if (!\is_array($a[$element])) {
                            $a[$element] = array($a[$element], (string)$value);
                        } else {
                            $a[$element][] = (string)$value;
                        }
                    }
                }
            }
        }

        if (\is_array($a)) {
            return $a;
        } else {
            return false;
        }
    }

    //Functions used to add child nodes to an existing DOMElement of a DOMDocument based on their data type
    static function xml_add_element_boolean_value($sNodeName, $bValue, $oDOMDocument, $oDOMElementParent)
    {
        if ($bValue)
            $oDOMElementChild = $oDOMDocument->createElement($sNodeName, "1");
        else
            $oDOMElementChild = $oDOMDocument->createElement($sNodeName, "0");
        $oDOMElementParent->appendChild($oDOMElementChild);
    }
    static function xml_add_element_key_value($sNodeName, $sValue, $oDOMDocument, $oDOMElementParent)
    {
        if ($sValue === "")
            $oDOMElementChild = $oDOMDocument->createElement($sNodeName, "-1");
        else
            $oDOMElementChild = $oDOMDocument->createElement($sNodeName, $sValue);
        $oDOMElementParent->appendChild($oDOMElementChild);
    }
    static function xml_add_element_number_value($sNodeName, $sValue, $oDOMDocument, $oDOMElementParent)
    {
        if ($sValue === "")
            $oDOMElementChild = $oDOMDocument->createElement($sNodeName, "-9999");
        else
            $oDOMElementChild = $oDOMDocument->createElement($sNodeName, $sValue);
        $oDOMElementParent->appendChild($oDOMElementChild);
    }
    static function xml_add_element_string_value($sNodeName, $sValue, $oDOMDocument, $oDOMElementParent)
    {
        $oDOMElementChild = $oDOMDocument->createElement($sNodeName, htmlspecialchars($sValue));
        $oDOMElementParent->appendChild($oDOMElementChild);
    }
    //NOTE: This replaces all instances in the dom tree
    static function xml_modify_element_value($sNodeName, $sNewValue, $oDOMDocument, $oDOMElementParent)
    {
        $oDOMDocument->getElementsByTagName($sNodeName)->item(0)->nodeValue = $sNewValue;
    }

    /**
     * @param \DateTime $dDateTime
     * @return string
     */
    static function format_date_time_for_db($dDateTime)
    {
        return $dDateTime->format(Globals::MYSQL_DATE_FORMAT);
    }

    /**
     * @param \DateTime $dDateTime
     * @return string
     */
    static function format_date_for_db($dDateTime)
    {
        return $dDateTime->format(Globals::MYSQL_DATE_ONLY_FORMAT);
    }

    /**
     * @param \DateTime $dDateTime
     * @return string
     */
    static function format_date_time_for_display($dDateTime, $sFormat = Globals::DATE_TIME_FORMAT_DISPLAY)
    {
        if (\is_null($dDateTime))
            return "";
        return $dDateTime->format($sFormat);
    }

    /**
     * @param string $sDateTime
     * @return string
     */
    static function format_date_time_string_for_display($sDateTime, $sFormat = Globals::DATE_TIME_FORMAT_DISPLAY)
    {
        if ($sDateTime === "")
            return "";
        return Globals::format_date_time_for_display(new \DateTime($sDateTime), $sFormat);
    }

    /**
     * @param \DateTime $dDateTime
     * @return string
     */
    static function format_date_for_display($dDate, $sFormat = Globals::DATE_FORMAT_DISPLAY)
    {
        if (\is_null($dDate))
            return "";
        return $dDate->format($sFormat);
    }

    /**
     * @param string $sDateTime
     * @return string
     */
    static function format_date_string_for_display($sDate, $sFormat = Globals::DATE_FORMAT_DISPLAY)
    {
        if ($sDate === "")
            return "";
        return Globals::format_date_for_display(new \DateTime($sDate), $sFormat);
    }

    /**
     * @param float $c
     * @param boolean $bAddDollarSign if true then a dollar sign is added to the front of the string
     * @return string is $c formatted as a currency string
     */
    static function format_currency($c, $bAddDollarSign = true)
    {
        if (is_numeric($c)) {
            if ($bAddDollarSign)
                return "$".number_format($c, 2);
            else
                return number_format($c, 2);
        } else
            return "";
    }

    /**
     * @param float $n
     * @param int $iDecimals is the number of decimal places
     * @return string is $n formatted
     */
    static function format_float($n, $iDecimals = 2)
    {
        if (is_numeric($n))
            return number_format($n, $iDecimals, ".", "");
        else
            return "";
    }

    /**
     * @param string $sTimeZone
     * @return \DateTime
     */
    static function get_current_date_time($sTimeZone = "")
    {
        //JPF 4/27/16 - Don't change the timezone for everything
        /*if ($sTimeZone != "") {
            date_default_timezone_set($sTimeZone);
        return new \DateTime();*/
        if ($sTimeZone != "")
            return new \DateTime("now", new \DateTimeZone($sTimeZone));
        else
            return new \DateTime();
    }

    /**
     * @param string $sTimeZone
     * @return \DateTime
     */
    static function get_current_date_time_string($sTimeZone = "")
    {
        $d = Globals::get_current_date_time($sTimeZone);
        return $d->format(Globals::MYSQL_DATE_FORMAT);
    }

    /**
     * Converts $sDateTime to the time zone given
     * @param string $sDateTime
     * @param string $sNewTimeZone
     * @return string is the formatted date time string
     */
    static function convert_date_time_string_to_time_zone($sDateTime, $sNewTimeZone, $sFormat = "m/d/Y h:i:s A", $sCurrentTimeZone = "America/New_York")
    {
        $dDateTime = new \DateTime($sDateTime);
        $dDateTime->setTimezone(new \DateTimeZone($sCurrentTimeZone));
        return Globals::convert_date_time_to_time_zone($dDateTime, $sNewTimeZone, $sFormat);
    }

    /**
     * Converts $dDateTime (in place) to the time zone given
     * @param \DateTime $dDateTime
     * @param string $sNewTimeZone
     * @return string is the formatted date time string
     */
    static function convert_date_time_to_time_zone(&$dDateTime, $sNewTimeZone, $sFormat = "m/d/Y h:i:s A")
    {
        $dDateTime->setTimezone(new \DateTimeZone($sNewTimeZone));
        return Globals::format_date_time_for_display($dDateTime, $sFormat);
    }

    /**
     * Creates a string concatenating all of the array items, separating the items by the $sSeparator string
     * @param array is an array of strings
     * @param boolean $bIncludeIfBlank if true then the value and separator will be included even if the value string is blank (empty)
     * @return string
     */
    static function concatenate_values($aValues, $sSeparator = " ", $bIncludeIfBlank = false)
    {
        $s = "";
        foreach ($aValues as $sValue) {
            if ($sValue !== "" || $bIncludeIfBlank) {
                if ($s !== "")
                    $s .= $sSeparator;
                $s .= $sValue;
            }
        }
        return $s;
    }

    /**
     * @param string $sMySQLDate is formatted in the MySQL format (Year-Month-Day Hour:Minute:Second)
     * @param string $sPart is "year", "month", "day", "hour", "minute", "second"
     * @param boolen $bRemoveLeadingZeroes
     */
    static function get_date_part($sMySQLDate, $sPart, $bRemoveLeadingZeroes = true)
    {
        switch (strtolower($sPart)) {
            case "year":
                $iValue = substr($sMySQLDate, 0, 4);
                break;
            case "month":
                $iValue = substr($sMySQLDate, 5, 2);
                break;
            case "day":
                $iValue = substr($sMySQLDate, 8, 2);
                break;
            case "hour":
                $iValue = substr($sMySQLDate, 11, 2);
                break;
            case "minute":
                $iValue = substr($sMySQLDate, 14, 2);
                break;
            case "second":
                $iValue = substr($sMySQLDate, 17, 2);
                break;
            default:
                $iValue = "";
        }
        if ($bRemoveLeadingZeroes)
            return ltrim($iValue, "0");
        else
            return $iValue;
    }

    /**
     * Convert an image from one format to another
     * @param $sImageDataIn string is the image data
     * @param $sImageDataOut string is the converted image data
     * @param $sFormatFrom string is the format to convert from (JPG, GIF, PNG, SVG, etc)
     * @param $sFormatFrom string is the format to convert to (JPG, GIF, PNG, SVG, etc)
     * @param $iWidth int is the width in pixels of the resulting image (0 means don't scale)
     * @param $iHeight int is the height in pixels of the resulting image (0 means don't scale)
     * @return string is blank if successful; ootherwise, it is an error message
     */
    static function convert_image($sImageDataIn, &$sImageDataOut, $sFormatFrom, $sFormatTo, $iWidth = 0, $iHeight = 0, $sAdditionalParameters = "")
    {
        try {
            $sImageDataOut = "";
            //Set the specs to read from STDIN and write to STDOUT
            $aSpecs = array(0 => array("pipe", "r"), 1 => array("pipe", "w"));
            //Open the process that will run the ImageMagick converter
            if ($iWidth == 0)
                $resource = proc_open("convert ".$sAdditionalParameters." ".$sFormatFrom.":- ".$sFormatTo.":-", $aSpecs, $aPipes);
            else
                $resource = proc_open("convert -scale ".$iWidth."x".$iHeight." ".$sAdditionalParameters." ".$sFormatFrom.":- ".$sFormatTo.":-", $aSpecs, $aPipes);
            //Write the encoded SVG data out to STDIN
            fwrite($aPipes[0], base64_decode($sImageDataIn));
            fclose($aPipes[0]);
            //Get the image data from STDOUT
            while (!feof($aPipes[1]))
                $sImageDataOut .= fread($aPipes[1], 1024);     //Read a chunk at a time
            fclose($aPipes[1]);
            //Close the process
            proc_close($resource);
            if ($sImageDataOut == "")
                return "Conversion Process Produced a Blank Image";
        } catch (\Exception $exc) {
            return $exc->getMessage();
        }
        return "";
    }

    // Encode a string to URL-safe base64
    static function encodeBase64UrlSafe($value)
    {
        return str_replace(array('+', '/'), array('-', '_'),base64_encode($value));
    }

    // Decode a string from URL-safe base64
    static function decodeBase64UrlSafe($value)
    {
        return base64_decode(str_replace(array('-', '_'), array('+', '/'),$value));
    }

    // Takes a float and returns a dollar value
    // Used for pricing calculations, etc.
    static function round_currency($p)
    {
        return number_format($p, 2, ".", "");
    }

    /**
     * @param string $sString is a string of delimited tag/value data
     * @param string $sTag is the Tag to search for
     * @param string $sDelimiter is the string/character used to separate the tag/value elements
     * @param string $sDefaultValue is returned if the tag is not found
     * @param string $sTagValueSeparator is the string/character used to separate the tag from the value
     * @return string
     */
    static function get_tag_value_from_delimited_string($sString, $sTag, $sDelimiter = ";", $sDefaultValue = "", $sTagValueSeparator = "=")
    {
        if (\is_array($aElements = explode($sDelimiter, $sString))) {
            foreach($aElements as $sTagValue) {
                if (($iPos = strpos($sTagValue, $sTagValueSeparator)) !== false) {
                    if (trim(substr($sTagValue, 0, $iPos)) == $sTag) {
                        return trim(substr($sTagValue, $iPos + strlen($sTagValueSeparator)));
                    }
                }
            }
        }
        return $sDefaultValue;
    }

    /**
     * Removes special characters and returns\line feeds (if $bRemoveReturns is true) from string
     * @param string $s
     * @return string
     */
    static function remove_special_characters($s, $bRemoveReturns = false)
    {
        $sResult = "";
        $a = str_split($s);
        $l = count($a);
        for ($i = 0; $i < $l; $i++) {
            $c = ord($a[$i]);
            if ($c > 31 && $c < 127) {
                $sResult .= chr($c);
            } elseif (!$bRemoveReturns && ($c == 10 || $c == 12)) {
                $sResult .= chr($c);
            }
        }
        return $sResult;
    }

    static function is_us_state($s)
    {
        $aStates = array("AK", "AL", "AR", "AS", "AZ", "CA", "CO", "CT", "DC", "DE", "FL", "GA", "GU", "HI", "IA", "ID", "IL", "IN", "KS", "KY", "LA", "MA", "MD", "ME", "MI", "MN", "MO", "MP", "MS", "MT", "NC", "ND", "NE", "NH", "NJ", "NM", "NV", "NY", "OH", "OK", "OR", "PA", "PR", "RI", "SC", "SD", "TN", "TX", "UM", "UT", "VA", "VI", "VT", "WA", "WI", "WV", "WY");
        return \in_array($s, $aStates);
    }
}
