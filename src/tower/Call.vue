<template>
  <div id="record-view" data-page="call" :data-search-mode="searchMode">
    <app-titlebar :title="'Call · ' + callKey" v-if="!searchMode">
      <spotlight-control
        :noun="viewConfig.searchNoun"
        @setFilters="setFilters"
        @resetFilters="setFilters">
      </spotlight-control>
    </app-titlebar>

    <div class="loader" v-if="loaderVisible"></div>

    <section class="content-section">
      <div class="_liner" v-show="!loaderVisible">
        <main class="sections">
          <section class="pinned-sections" :data-pinned="sectionsArePinned">
            <div class="_liner">
              <form-section
                v-for="(section, name) in pinnedSections"
                :key="name"
                :id="name"
                :title="section.title"
                :fixed="true">
                <component
                  :is="section.componentName"
                  :call="call"
                  :searchMode="searchMode"
                  @addSpeedBump="addSpeedBump">
                </component>
              </form-section>
            </div>
          </section>

          <section class="floating-sections" :data-pinned="sectionsArePinned">
            <form-section
              v-for="(section, name) in floatingSections"
              :key="name"
              :id="name"
              :title="section.title"
              :fixed="true">
              <component
                :is="section.componentName"
                :call="call"
                :searchMode="searchMode"
                :price-repository="priceRepository"
                @addSpeedBump="addSpeedBump"
                @setBalance="value => { liveBalance = Number(value) }">
              </component>
            </form-section>

            <app-modified-metadata
              class="modified-metadata"
              style="margin-top: 1rem"
              v-if="!isNewRecord && !searchMode"
              :record="call"
              :config="viewConfig"
              modifiedAtAlias="dLastModified"
              modifiedByAlias="lUserKey_LastModified"
              recordKeyAlias="lCallKey">
            </app-modified-metadata>
          </section>
        </main>

        <aside class="mini-map">
          <div class="_liner">

            <CallSketch :call="call"
              :searchMode="searchMode"
              :priceRepository="priceRepository"
              :liveBalance="liveBalance" />

            <ul class="_links">
              <li class="_link"
                v-for="(section, name) in sectionsProxy"
                @click="lookAt(name)"
                :key="name"
                :data-pinned="sectionsArePinned && ['tow', 'vehicle'].includes(name)">
                {{ section.title }}
              </li>
            </ul>

          </div>

        </aside>
      </div>
    </section>

    <app-footerbar v-if="!searchMode">
      <template slot="left">
        <app-button @click="saveAndClose()" type="primary" :disabled="!canSave">Save</app-button>
        <app-button @click="save()" :disabled="!canSave">Save &amp; Stay</app-button>
        <app-button @click="duplicate" :disabled="!canDuplicate">Duplicate</app-button>
        <app-button @click="toggleActions(true)" :disabled="!canDoAction">Actions</app-button>
        <app-button @click="cancel" :disabled="!canClose">Close</app-button>
      </template>

      <app-button type="success" size="normal" @click="addCall" v-if="canAddCall" style="border-radius: 10rem;">
        <i class="far fa-plus"></i>&nbsp;Add
      </app-button>
    </app-footerbar>

    <actions
      :show="actionsComponent.visible"
      :call-key="callKey"
      :subterminal-key="subcompanyKeyProxy"
      :mileage-required="call.bMileageRequired"
      :mutations="actionsComponent.mutations"
      :dates-visible="actionsComponent.datesVisible"
      @close="toggleActions(true)"
      @notify="notify">
    </actions>

    <DMVCommunications v-if="showDMV" :callKey="callKey"  @close="onHideDMVModal" />
  </div>
</template>

<script>
import Access from '@/utils/access';
import { mapGetters, mapActions } from 'vuex';

import HoldsMixin from '@/mixins/holds_mixin.js';
import PaymentMixin from '@/mixins/payment_mixin.js';
import { callMixin } from '@/mixins/call_mixin.js';
import useTowType from '@/components/inputs/TowType/useTowType';

import Actions from '@/tower/actions/Actions.vue';
import SpotlightControl from '@/components/features/SpotlightControl.vue';
import DMVCommunications from '@/components/features/DMVCommunications.vue';
import CallSketch from '@/components/call/Sketch.vue';

import TowSection from '@/components/call/TowSection.vue';
import LienSection from '@/components/call/LienSection.vue';
import NotesSection from '@/components/call/NotesSection/Index.vue';
import HoldsSection from '@/components/call/HoldsSection.vue';
import RetowSection from '@/components/call/RetowSection.vue';
import CancelSection from '@/components/call/CancelSection.vue';
import ImagesSection from '@/components/filegallery/ImagesSection.vue';
import PaymentSection from '@/components/call/PaymentSection.vue';
import PricingSection from '@/components/call/PricingSection/Index.vue';
import VehicleSection from '@/components/call/VehicleSection.vue';
import ContactSection from '@/components/call/ContactSection.vue';
import MileageSection from '@/components/call/MileageSection.vue';
import DispatchSection from '@/components/call/DispatchSection.vue';
import LegsSection from '@/components/call/LegsSection/Index.vue';
import InventorySection from '@/components/call/InventorySection.vue';
import AccountingSection from '@/components/call/AccountingSection.vue';
import InspectionsSection from '@/components/call/InspectionsSection.vue';
import SubterminalSection from '@/components/call/SubterminalSection.vue';
import SalePaymentSection from '@/components/call/SalePaymentSection.vue';
import SalePricingSection from '@/components/call/SalePricingSection/SalePricingSection.vue';
import MiscellaneousSection from '@/components/call/MiscellaneousSection.vue';
import RetowDispatchSection from '@/components/call/RetowDispatchSection.vue';
import MotorClubBillingSection from '@/components/call/MotorClubBillingSection.vue';
import SystemTagsSection from '@/components/call/SystemTagsSection/Index.vue';

import SearchTowSection from '@/components/call_search/TowSection.vue';
import SearchCallSection from '@/components/call_search/CallSection.vue';
import SearchLienSection from '@/components/call_search/LienSection.vue';
import SearchNotesSection from '@/components/call_search/NotesSection.vue';
import SearchHoldsSection from '@/components/call_search/HoldsSection.vue';
import SearchRetowSection from '@/components/call_search/RetowSection.vue';
import SearchCancelSection from '@/components/call_search/CancelSection.vue';
import SearchPaymentSection from '@/components/call_search/PaymentSection.vue';
import SearchPricingSection from '@/components/call_search/PricingSection.vue';
import SearchVehicleSection from '@/components/call_search/VehicleSection.vue';
import SearchContactSection from '@/components/call_search/ContactSection.vue';
import SearchMileageSection from '@/components/call_search/MileageSection.vue';
import SearchDispatchSection from '@/components/call_search/DispatchSection.vue';
import SearchInventorySection from '@/components/call_search/InventorySection.vue';
import SearchAccountingSection from '@/components/call_search/AccountingSection.vue';
import SearchInspectionsSection from '@/components/call_search/InspectionsSection.vue';
import SearchSalePaymentSection from '@/components/call_search/SalePaymentSection.vue';
import SearchSalePricingSection from '@/components/call_search/SalePricingSection.vue';
import SearchSubterminalSection from '@/components/call_search/SubterminalSection.vue';
import SearchMiscellaneousSection from '@/components/call_search/MiscellaneousSection.vue';

import {
  VALUE_ID,
  COMPANY_ID,

  BEFORE_CALL_READ,
  AFTER_CALL_READ,
  EVENT_ALTER_CALL_COMPLETE,
  EVENT_TOGGLE_CALL_SECTION,
  EVENT_SEARCH_THROW_BOOMERANG,
  EVENT_SEARCH_CATCH_BOOMERANG,
  EVENT_TRIGGER_CALL_READ,

  CALL_SECTION_NOTES,
  CALL_SECTION_ACCOUNTING,
  CALL_SECTION_TOW_PRICING,
  CALL_SECTION_TOW_DISPATCH,
  CALL_SECTION_LIEN,
  CALL_SECTION_INVENTORY,
  CALL_SECTION_CANCEL,
  CALL_SECTION_RETOW,
  CALL_SECTION_RETOW_DISPATCH,
  CALL_SECTION_MOTOR_CLUB_BILLING,
  CALL_SECTION_SALE_PAYMENTS,
  CALL_SECTION_SALE_PRICING,
  CALL_SECTION_LEGS
} from '@/config';

export default {
  name: 'call',

  mixins: [
    HoldsMixin,
    PaymentMixin,
    callMixin
  ],

  components: {
    Actions,
    SpotlightControl,
    DMVCommunications,
    CallSketch,

    TowSection,
    LienSection,
    NotesSection,
    HoldsSection,
    RetowSection,
    CancelSection,
    ImagesSection,
    ContactSection,
    MileageSection,
    PaymentSection,
    PricingSection,
    VehicleSection,
    DispatchSection,
    LegsSection,
    InventorySection,
    AccountingSection,
    InspectionsSection,
    SubterminalSection,
    SalePaymentSection,
    SalePricingSection,
    MiscellaneousSection,
    RetowDispatchSection,
    MotorClubBillingSection,
    SystemTagsSection,

    SearchTowSection,
    SearchCallSection,
    SearchLienSection,
    SearchNotesSection,
    SearchHoldsSection,
    SearchRetowSection,
    SearchCancelSection,
    SearchContactSection,
    SearchMileageSection,
    SearchPaymentSection,
    SearchPricingSection,
    SearchVehicleSection,
    SearchDispatchSection,
    SearchInventorySection,
    SearchAccountingSection,
    SearchInspectionsSection,
    SearchSalePaymentSection,
    SearchSalePricingSection,
    SearchSubterminalSection,
    SearchMiscellaneousSection
  },

  props: {
    searchMode: { type: Boolean, default: false }
  },

  provide () {
    return {
      lookAt: this.lookAt,
      onShowDMVModal: this.onShowDMVModal,
      interruptExit: this.interruptExit
    };
  },

  data () {
    const towTypeUtils = useTowType();

    return {
      showDMV: false,

      viewConfig: {
        noun: 'Call',
        searchNoun: 'CallSearch',
        recordKeyName: 'CAL_lCallKey',
        returnRouteName: 'Calls',
        addRouteName: 'AddCall',
        routeOnClose: null
      },

      lastReadAt: null,
      liveBalance: 0,
      subterminals: [],
      callStatuses: [],
      finalDispositions: [],
      loaderVisible: true,
      sectionsArePinned: false,
      towTypeUtils,

      // When present, prompt the user to save the call before navigating away
      exitSpeedBumps: [],

      actionsComponent: {
        mutations: {},
        visible: false,
        datesVisible: true
      },

      callSectionDetails: [
        'accounting',
        'cancel',
        'lien',
        'towpricing',
        'dispatch',
        'holds'
      ],

      sections: {
        call: {
          title: 'Call',
          componentName: 'search-call-section',
          landingPoint: 'CAL_lReferenceNum',
          enabled: false,
          pinned: false
        },
        subterminal: {
          title: 'Company',
          componentName: this.searchMode ? 'search-subterminal-section' : 'subterminal-section',
          landingPoint: 'CAL_lSubterminalKey',
          enabled: false,
          pinned: false
        },
        tow: {
          title: 'Tow',
          componentName: this.searchMode ? 'search-tow-section' : 'tow-section',
          landingPoint: 'CAL_vc30ContactName',
          enabled: true,
          pinned: true
        },
        vehicle: {
          title: 'Vehicle',
          componentName: this.searchMode ? 'search-vehicle-section' : 'vehicle-section',
          landingPoint: 'CAL_iYear',
          enabled: true,
          pinned: false
        },
        mileage: {
          title: 'Mileage',
          componentName: this.searchMode ? 'search-mileage-section' : 'mileage-section',
          landingPoint: '',
          enabled: true,
          pinned: false
        },
        miscellaneous: {
          title: 'Miscellaneous',
          componentName: this.searchMode ? 'search-miscellaneous-section' : 'miscellaneous-section',
          landingPoint: 'CAL_tPriority',
          enabled: true,
          pinned: false
        },
        cancel: {
          title: 'Cancel',
          componentName: this.searchMode ? 'search-cancel-section' : 'cancel-section',
          landingPoint: 'cancel.add',
          enabled: true,
          pinned: false
        },
        towdispatch: {
          title: 'Dispatches',
          componentName: this.searchMode ? 'search-dispatch-section' : 'dispatch-section',
          landingPoint: '',
          enabled: true,
          pinned: false
        },
        legs: {
          title: 'Legs',
          componentName: 'legs-section',
          landingPoint: '',
          enabled: true,
          pinned: false
        },
        system_tags: {
          title: 'System Tags',
          componentName: 'system-tags-section',
          landingPoint: '',
          enabled: !this.searchMode,
          pinned: false
        },
        towpricing: {
          title: 'Pricing',
          componentName: this.searchMode ? 'search-pricing-section' : 'pricing-section',
          landingPoint: 'pricing_AddService',
          enabled: true,
          pinned: false
        },
        inventory: {
          title: 'Inventory',
          componentName: this.searchMode ? 'search-inventory-section' : 'inventory-section',
          landingPoint: 'INV_lStorageLotKey',
          enabled: true,
          pinned: false
        },
        towpayment: {
          title: 'Payments',
          componentName: this.searchMode ? 'search-payment-section' : 'payment-section',
          landingPoint: 'payment.add',
          enabled: true,
          pinned: false
        },
        holds: {
          title: 'Holds',
          componentName: this.searchMode ? 'search-holds-section' : 'holds-section',
          landingPoint: 'hold.add',
          enabled: true,
          pinned: false
        },
        notes: {
          title: 'Notes',
          componentName: this.searchMode ? 'search-notes-section' : 'notes-section',
          landingPoint: 'note.add',
          enabled: true,
          pinned: false
        },
        inspection: {
          title: 'Inspections',
          componentName: this.searchMode ? 'search-inspections-section' : 'inspections-section',
          landingPoint: 'inspection.add',
          enabled: true,
          pinned: false
        },
        contacts: {
          title: 'Contacts',
          componentName: this.searchMode ? 'search-contact-section' : 'contact-section',
          landingPoint: 'contact.add',
          enabled: true,
          pinned: false
        },
        lien: {
          title: 'Lien',
          componentName: this.searchMode ? 'search-lien-section' : 'lien-section',
          landingPoint: '',
          enabled: true,
          pinned: false
        },
        images: {
          title: 'Images',
          componentName: 'images-section',
          landingPoint: '',
          enabled: !this.searchMode,
          pinned: false
        },
        retow: {
          title: 'Retow',
          componentName: this.searchMode ? 'search-retow-section' : 'retow-section',
          landingPoint: 'RET_vc100Location',
          enabled: true,
          pinned: false
        },
        retowdispatch: {
          title: 'Re-dispatches',
          componentName: 'retow-dispatch-section',
          landingPoint: '',
          enabled: !this.searchMode,
          pinned: false
        },
        accounting: {
          title: 'Accounting',
          componentName: this.searchMode ? 'search-accounting-section' : 'accounting-section',
          landingPoint: 'CAL_vc255AccountingNotes',
          enabled: true,
          pinned: false
        },
        motor_club_billing: {
          title: 'Motor Club Invoice',
          componentName: 'motor-club-billing-section',
          landingPoint: '',
          enabled: true,
          pinned: false
        },
        sale_payments: {
          title: 'Sale',
          componentName: this.searchMode ? 'search-sale-payment-section' : 'sale-payment-section',
          landingPoint: '',
          enabled: true,
          pinned: false
        },
        sale_pricing: {
          title: 'Sale Pricing',
          componentName: this.searchMode ? 'search-sale-pricing-section' : 'sale-pricing-section',
          landingPoint: '',
          enabled: true,
          pinned: false
        }
      },

      priceRepository: {
        tow: {
          TaxRateOverride: '',
          TaxRate: '',
          DiscountPct: '',
          Total: '',
          TaxTotal: '',
          DiscountTotal: '',
          TaxExempt: false
        },
        sale: {
          TaxRateOverride: '',
          TaxRate: '',
          Total: '',
          TaxTotal: '',
          DiscountTotal: '',
          TaxExempt: false
        }
      },

      call: {
        dETA: '',
        iYear: '',
        ch5Zone: '',
        tcPrice: '',
        vc25VIN: '',
        lMakeKey: '',
        lCallKey: '',
        tPriority: this.searchMode ? 0 : 9,
        lModelKey: '',
        vc20PONum: '',
        vc20RONum: '',
        dCallTaken: '',
        dTagExpiry: '',
        fDiscountPct: 0,
        lTowTypeKey: '',
        ch2StateKey: '',
        bNoCharge: false,
        lCustomerKey: '',
        TowTicketNum: '',
        lBodyTypeKey: '',
        dAppointment: '',
        vc10Odometer: '',
        lColorTypeKey: '',
        lTruckTypeKey: '',
        vc20PoliceNum: '',
        vc30OwnerName: '',
        vc100Location: '',
        lReasonTypeKey: '',
        vc15LicenseNum: '',
        vc20OwnerPhone: '',
        vc10PoliceBeat: '',
        CallTakerNotes: '',
        lSubterminalKey: '',
        vc30ContactName: '',
        dExpirationDate: '',
        bOwnerWithVehicle: 0,
        vc50UserDefined2: '',
        vc50UserDefined3: '',
        vc100Destination: '',
        vc255DriverNotes: '',
        fTaxRate_Override: '',
        gcLocationLatitude: '',
        vc20MembershipNum: '',
        vc100UserDefined1: '',
        gcLocationLongitude: '',
        bPortalToPortal: false,
        vc255DispatchNotes: '',
        vc255DispatchNotes2: '',
        vc255DispatchNotes3: '',
        vc255DispatchNotes4: '',
        lCallStatusTypeKey: '',
        bMileageRequired: false,
        bSecondCommission: false,
        vc20ContactPhoneNum: '',
        vc255AccountingNotes: '',
        gcDestinationLatitude: '',
        vc30ExtraVehicleInfo: '',
        vc50EquipmentRequired: '',
        gcDestinationLongitude: '',
        DefaultPricingRetrieved: false,

        Holds: [],
        Retow: {},
        Contacts: [],
        Inventory: {},
        Dispatches: [],
        InspectionItems: []
      }
    };
  },

  computed: {
    ...mapGetters([
      '__state',
      'TOPSCOMPANY__settings'
    ]),

    isCallConfirmed () {
      return !this.$_.isEmpty(this.$_.get(this.call, 'dAccConfirm', ''));
    },

    canAddCall () {
      if (Number(this.__state.orgUnitKey) === COMPANY_ID.GRAND_RAPIDS_POLICE_DEPARTMENT) {
        return !Access.has('calls.duplicate');
      }

      return true;
    },

    callKey () {
      return this.$route.params.key;
    },

    subcompanyKeyProxy () {
      return this.call.lSubterminalKey || this.$store.state.topsCompany.settings.SubterminalKey;
    },

    isNewRecord () {
      return this.$_.isEmpty(this.callKey);
    },

    dispatchKey () {
      let dispatches = this.$_.get(this.call, 'Dispatches', []);
      let activeStatuses = [
        VALUE_ID.dispatchStatus.assigned,
        VALUE_ID.dispatchStatus.dispatched,
        VALUE_ID.dispatchStatus.acknowledged,
        VALUE_ID.dispatchStatus.arrived,
        VALUE_ID.dispatchStatus.hooked,
        VALUE_ID.dispatchStatus.dropped
      ];

      if (!dispatches) return;

      let dispatch = this.$_.head(this.$_.filter(dispatches, dispatch => this.$_.includes(activeStatuses, dispatch.lDispatchStatusTypeKey)));

      if (!dispatch) return;

      return this.$_.get(dispatch, 'lDispatchKey', '');
    },

    hasDispatches () {
      const dispatches = this.call.Dispatches || [];
      return dispatches.filter(dispatch => !dispatch.bRetow).length > 0;
    },

    hasRetowDispatches () {
      const dispatches = this.$_.get(this.call, 'Dispatches', []);
      return dispatches.filter(dispatch => dispatch.bRetow).length > 0;
    },

    hasLegs () {
      const legs = this.$_.get(this.call, 'Legs', []);
      return legs.length > 0;
    },

    sectionsProxy () {
      let modifiedSectionsObject = {};
      let hiddenSections = [];

      if (!Access.has('calls.price')) {
        hiddenSections.push(CALL_SECTION_TOW_PRICING);
      }

      if (!Access.has('calls.notes')) {
        hiddenSections.push(CALL_SECTION_NOTES);
      }

      if (Access.has('calls.restrictDispatch')) {
        hiddenSections.push(CALL_SECTION_TOW_DISPATCH);
      }

      if (Access.has('calls.restrictAccounting')) {
        hiddenSections.push(CALL_SECTION_ACCOUNTING);
      }

      if (!this.$_.has(this.call, 'Lien')) {
        hiddenSections.push(CALL_SECTION_LIEN);
      }

      if (!this.hasDispatches && !this.searchMode) {
        hiddenSections.push(CALL_SECTION_TOW_DISPATCH);
      }

      if (!this.hasRetowDispatches && !this.searchMode) {
        hiddenSections.push(CALL_SECTION_RETOW_DISPATCH);
      }

      if (!this.$_.has(this.call, 'Inventory')) {
        hiddenSections.push(CALL_SECTION_INVENTORY);
      }

      if (!this.$_.get(this.call, 'dCallCanceled', '')) {
        hiddenSections.push(CALL_SECTION_CANCEL);
      }

      if (!this.$_.has(this.call, 'Retow')) {
        hiddenSections.push(CALL_SECTION_RETOW);
      }

      if (!this.$_.has(this.call, 'MCInvoice')) {
        hiddenSections.push(CALL_SECTION_MOTOR_CLUB_BILLING);
      }

      if (!this.searchMode && !this.isSaleEnabled) {
        hiddenSections.push(CALL_SECTION_SALE_PAYMENTS);
        hiddenSections.push(CALL_SECTION_SALE_PRICING);
      }

      if (!this.hasLegs) {
        hiddenSections.push(CALL_SECTION_LEGS);
      } else {
        hiddenSections.push(CALL_SECTION_TOW_DISPATCH);
      }

      this.$_.forEach(this.sections, (section, key) => {
        if (section.enabled && !this.$_.includes(hiddenSections, key)) {
          this.$_.set(modifiedSectionsObject, key, section);
        }
      });

      return modifiedSectionsObject;
    },

    pinnedSections () {
      let sections = {};

      this.$_.forEach(this.sectionsProxy, (section, key) => {
        if (section.pinned) {
          this.$_.set(sections, key, section);
        }
      });

      return sections;
    },

    floatingSections () {
      let sections = {};

      this.$_.forEach(this.sectionsProxy, (section, key) => {
        if (!section.pinned) {
          this.$_.set(sections, key, section);
        }
      });

      return sections;
    },

    paymentsProxy () {
      return this.$_.get(this.call, 'TowPayments', []);
    },

    inspectionItemsProxy () {
      return this.$_.get(this.call, 'InspectionItems', []);
    },

    orderLinesProxy () {
      return this.$_.get(this.call, 'TowOrderLines', []);
    },

    hasIncompletePayment () {
      let incompletePayments = this.$_.filter(this.paymentsProxy, payment => {
        return !payment.lPaymentTypeKey ||
          !payment.lReceiptTypeKey ||
          !payment.lReceivedBy ||
          !payment.tcAmount;
      });

      return incompletePayments.length > 0;
    },

    hasIncompleteInspection () {
      let incompleteInspections = this.$_.filter(this.inspectionItemsProxy, payment => {
        return !payment.vc255Value;
      });

      return incompleteInspections.length > 0;
    },

    hasIncompleteOrderLine () {
      let incompleteOrderLine = this.$_.filter(this.orderLinesProxy, orderLine => {
        return Number(orderLine.lServicePricingTypeKey) === VALUE_ID.pricingType.dispatch &&
          !orderLine.lDispatchKey &&
          Number(orderLine.bRetow) === 0;
      });

      return incompleteOrderLine.length > 0 &&
        ![VALUE_ID.callStatus.unassigned, VALUE_ID.callStatus.cancelled].includes(Number(this.call.lCallStatusTypeKey));
    },

    isDropoffTowType () {
      if (!this.call || !this.call.lTowTypeKey) return false;

      return this.towTypeUtils.isDropoffTowType(this.call.lTowTypeKey);
    },

    hasRequiredFieldsForDropoff () {
      if (!this.call) return true;
      if (!this.isDropoffTowType) return true;

      const hasDestinationLot = this.call.vc100Destination && this.call.vc100Destination.length > 0;

      const hasTowTicket = this.call.InvoiceNumEqualCallNum ||
        this.call.TowTicketNum;

      return hasDestinationLot && hasTowTicket;
    },

    canSave () {
      if (!this.callKey) return false;
      if (!this.$store.state.call.isCallReelEnabled) return false;
      if (this.hasIncompletePayment) return false;
      if (this.hasIncompleteInspection) return false;
      if (this.hasIncompleteOrderLine) return false;
      if (!this.hasRequiredFieldsForDropoff) return false;

      if (this.isCallConfirmed) {
        if (Access.has('calls.fullEditAfterConfirmed')) return true;
        return Access.has('calls.partialEditAfterConfirmed');
      }

      return Access.has('calls.edit');
    },

    canDuplicate () {
      if (!this.$store.state.call.isCallReelEnabled) return false;
      return !Access.has('calls.duplicate');
    },

    canClose () {
      return this.$store.state.call.isCallReelEnabled;
    },

    canDoAction () {
      return this.$store.state.call.isCallReelEnabled;
    },

    isSaleEnabled () {
      return this.callStatusIs([VALUE_ID.callStatus.completed, VALUE_ID.callStatus.confirmed]) ||
        this.finalDispositionIs(['Auctioned', 'Scrapped', 'Sold']) ||
        this.$_.get(this.call, 'SalePayments', []).length > 0;
    }
  },

  methods: {
    ...mapActions([
      '__getNow',
      'TOPSCALL__update',
      'CALL__getStatuses',
      'CALL__getSubterminals',
      'CALL__getFinalDispositions'
    ]),

    onShowDMVModal () {
      this.showDMV = true;
    },

    onHideDMVModal () {
      this.showDMV = false;
    },

    addSpeedBump (bump) {
      let bumps = [...this.exitSpeedBumps, bump];

      this.exitSpeedBumps = this.$_.uniq(bumps);
    },

    resetSpeedBumps () {
      this.exitSpeedBumps = [];
    },

    interruptExit () {
      return new Promise((resolve, reject) => {
        if (this.exitSpeedBumps.length === 0) {
          resolve();
          return;
        }

        this.$confirm('You may want to save these changes first: ' + this.exitSpeedBumps.join(', '), 'Unsaved Changes', {
          confirmButtonText: 'Ignore',
          cancelButtonText: 'Cancel',
          type: 'warning'
        }).then(() => {
          this.resetSpeedBumps();
          resolve();
        }).catch(() => {
          this.resetSpeedBumps();
          reject();
        });
      });
    },

    async addCall () {
      await this.interruptExit();

      this.$router.push({ name: 'AddCall' });
    },

    callStatusIs (keys) {
      keys = this.$_.castArray(keys);

      return keys.includes(this.call.lCallStatusTypeKey);
    },

    finalDispositionIs (values) {
      values = this.$_.castArray(values);
      const targetDispositionKey = this.call.lFinalDispositionTypeKey;
      let matchFound = false;

      values.forEach(value => {
        const disposition = this.$_.find(this.finalDispositions, ['Value', value]);

        if (disposition && disposition.Key === targetDispositionKey) matchFound = true;
      });

      return matchFound;
    },

    setPinnedSections () {
      let titleBar = window.getComputedStyle(document.querySelector('.title-bar'));
      let footerBar = window.getComputedStyle(document.querySelector('.footer-bar'));
      let content = window.getComputedStyle(document.querySelector('.content-section'));
      let pinnedSections = window.getComputedStyle(document.querySelector('.pinned-sections ._liner'));

      let pinnedSectionsElementHeight = Number(pinnedSections.height.replace('px', ''));
      let viewareaHeight = window.visualViewport.height -
        Number(titleBar.height.replace('px', '')) -
        Number(footerBar.height.replace('px', '')) -
        Number(content.paddingTop.replace('px', '')) -
        Number(content.paddingBottom.replace('px', ''));

      this.sectionsArePinned = pinnedSectionsElementHeight < viewareaHeight;
    },

    lookAt (section) {
      let content = window.getComputedStyle(document.querySelector('.content-section'));
      let offset = Number(content.paddingTop.replace('px', ''));

      if (this.sectionsArePinned) {
        let sections = window.getComputedStyle(document.querySelector('.sections'));
        let vehicleSection = window.getComputedStyle(document.querySelector('#vehicle'));

        offset = offset +
          Number(vehicleSection.height.replace('px', '')) +
          Number(sections.gap.replace('px', ''));
      }

      this.$gsap.to('.content-section', {
        duration: 0.8,
        ease: 'power4.out',
        scrollTo: {
          y: `#${section}`,
          offsetY: offset
        }
      });
    },

    async getNow () {
      return new Promise((resolve, reject) => {
        this.__getNow({
          callback: response => {
            resolve(response.Now);
          }
        });
      });
    },

    async duplicate () {
      await this.interruptExit();

      this.$router.replace({
        name: this.viewConfig.addRouteName,
        query: { duplicate: this.callKey }
      });
    },

    async cancel () {
      await this.interruptExit();

      if (this.trajectoryRoute) {
        this.$router.push({ name: this.trajectoryRoute });
      } else if (this.viewConfig.routeOnClose) {
        this.$router.push({ name: this.viewConfig.routeOnClose });
      } else if (this.$store.state.addCall.shouldStayOnSave) {
        this.$router.push({ name: 'Calls' });
      } else {
        this.$router.go(-1);
      }
    },

    async readCall () {
      if (this.searchMode) return;

      this.$store.state.call.isTowPricingGuardActive = false;
      this.$store.state.call.isCallReelEnabled = false;
      this.$hub.$emit(BEFORE_CALL_READ);

      this.$store.dispatch('TOPSCALL__read', {
        callKey: this.callKey,
        details: this.callSectionDetails,
        success: async response => {
          this.call = response;
          this.loaderVisible = false;
          this.lastReadAt = await this.getNow();

          setTimeout(() => {
            this.$store.state.call.isTowPricingGuardActive = true;
            this.$store.state.call.isCallReelEnabled = true;
          }, 1000);
          this.$hub.$emit(AFTER_CALL_READ);

          await this.$awaitNextTicks(2);

          this.setPinnedSections();
        }
      });
    },

    getStatuses () {
      this.CALL__getStatuses({
        callback: response => {
          this.callStatuses = response;
        }
      });
    },

    async saveAndClose () {
      await this.save(true);
    },

    async save (close = false) {
      if (!this.canSave) return false;

      this.resetSpeedBumps();

      // if (this.beforeSave) this.beforeSave();

      this.TOPSCALL__update({
        lastRead: this.lastReadAt,
        call: this.call,
        callback: response => {
          // if (this.afterSave) this.afterSave();

          if (close) {
            if (this.trajectoryRoute) {
              this.$router.push({ name: this.trajectoryRoute });
            } else if (this.viewConfig.routeOnClose) {
              this.$router.push({ name: this.viewConfig.routeOnClose });
            } else {
              this.$router.go(-1);
            }

            return;
          }

          this.readCall();
        },
        failCallback: response => {
          if (this.hasVINErrorMessage(response)) {
            this.showVINErrorPrompt(response.Message);
          }
        }
      });
    },

    hasVINErrorMessage (response) {
      return this.$_.has(response, 'Message') && this.$_.includes(response.Message, 'VIN');
    },

    showVINErrorPrompt (message) {
      this.$confirm(message, 'Invalid VIN', {
        confirmButtonText: 'Save Anyway',
        cancelButtonText: 'Cancel',
        type: 'warning'
      }).then(() => {
        this.call.IgnoreBadVIN = true;
        this.save();
      }).catch(() => {
        this.call.IgnoreBadVIN = false;
      });
    },

    async toggleActions (showDates = true) {
      await this.interruptExit();

      this.actionsComponent.visible = !this.actionsComponent.visible;
      this.actionsComponent.datesVisible = showDates;

      if (!this.actionsComponent.visible) {
        this.readCall();
      }
    },

    toggleSection (name, enabled = null) {
      if (!this.$_.has(this.sections, name)) return;

      this.sections[name].enabled = enabled || !this.sections[name].enabled;
    },

    notify (payload) {
      this.$router.replace({
        name: 'Notify',
        query: {
          callKey: this.$_.get(payload, 'callKey', ''),
          dispatchKey: this.$_.get(payload, 'dispatchKey', ''),
          dispatchDriverKey: this.$_.get(payload, 'dispatchDriverKey', ''),
          dispatchTruckKey: this.$_.get(payload, 'dispatchTruckKey', ''),
          dispatchEmployeeKey: this.$_.get(payload, 'dispatchEmployeeKey', ''),
          reason: this.$_.get(payload, 'reason', '')
        }
      });
    },

    setFilters () {
      this.$router.push({
        name: this.viewConfig.returnRouteName
      });
    },

    getSubterminals () {
      this.CALL__getSubterminals({
        callback: response => {
          this.subterminals = response;
          const activeSubterminals = this.subterminals.filter(subterminal => subterminal.Active);
          this.sections.subterminal.enabled = activeSubterminals.length > 1;
        }
      });
    }
  },

  mounted () {
    if (this.searchMode) {
      this.loaderVisible = false;
    }

    this.CALL__getFinalDispositions({
      callback: response => {
        this.finalDispositions = response;
      }
    });

    this.getStatuses();
    this.readCall();
    this.getSubterminals();

    window.addEventListener('resize', this.setPinnedSections);

    this.$hub.$on(EVENT_ALTER_CALL_COMPLETE, async payload => {
      this.actionsComponent.mutations = payload.mutations;
      this.toggleActions(false);
    });

    this.$hub.$on(EVENT_TRIGGER_CALL_READ, () => {
      this.readCall();
    });

    this.$hub.$on(EVENT_TOGGLE_CALL_SECTION, ({ section, enabled }) => {
      this.toggleSection(section, enabled);
    });

    this.$hub.$on(EVENT_SEARCH_THROW_BOOMERANG, onSuccess => {
      this.$hub.$emit(EVENT_SEARCH_CATCH_BOOMERANG, {
        noun: this.viewConfig.searchNoun,
        data: this.call,
        onSuccess: onSuccess
      });
    });
  },

  beforeDestroy () {
    window.removeEventListener('resize', this.setPinnedSections);
    this.$hub.$off(EVENT_ALTER_CALL_COMPLETE);
    this.$hub.$off(EVENT_TRIGGER_CALL_READ);
    this.$hub.$off(EVENT_TOGGLE_CALL_SECTION);
    this.$hub.$off(EVENT_SEARCH_THROW_BOOMERANG);
  },

  beforeRouteLeave (to, from, next) {
    this.call.lTowTypeKey = '';
    next();
  },
};
</script>

<style scoped>
#call-section {
  background: white;
  width: 10rem;
  border-radius: 0.25rem;
}
</style>
